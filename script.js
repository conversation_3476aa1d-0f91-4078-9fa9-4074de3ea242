// Global variables
let currentSection = 0;
let candlesBlown = 0;
const totalCandles = 3;

// Start the birthday journey
function startJourney() {
    const welcomeSection = document.getElementById('welcome');
    const cardSection = document.getElementById('cardSection');
    
    // Fade out welcome section
    welcomeSection.style.transition = 'opacity 1s ease-out';
    welcomeSection.style.opacity = '0';
    
    setTimeout(() => {
        welcomeSection.classList.add('hidden');
        cardSection.classList.remove('hidden');
        cardSection.style.opacity = '0';
        cardSection.style.transition = 'opacity 1s ease-in';
        
        setTimeout(() => {
            cardSection.style.opacity = '1';
        }, 100);
    }, 1000);
    
    currentSection = 1;
}

// Initialize card interactions
document.addEventListener('DOMContentLoaded', function() {
    const birthdayCard = document.getElementById('birthdayCard');
    const messageCards = document.querySelectorAll('.message-card');

    // Birthday card flip interaction
    if (birthdayCard) {
        birthdayCard.addEventListener('click', function() {
            this.classList.toggle('flipped');

            // After card is opened, show messages section
            if (this.classList.contains('flipped')) {
                setTimeout(() => {
                    showNextSection('messagesSection');
                }, 2000);
            }
        });
    }

    // Message cards flip interaction
    messageCards.forEach(card => {
        card.addEventListener('click', function() {
            this.classList.toggle('flipped');

            // Check if all message cards are flipped
            const flippedCards = document.querySelectorAll('.message-card.flipped');
            if (flippedCards.length === messageCards.length) {
                setTimeout(() => {
                    showNextSection('cakeSection');
                }, 1500);
            }
        });
    });


});

// Candle blowing function
function blowCandle(flame, candleNumber) {
    if (!flame.classList.contains('blown-out')) {
        flame.classList.add('blown-out');
        candlesBlown++;

        // Add blow effect
        createBlowEffect(flame);

        // Check if all candles are blown out
        if (candlesBlown === totalCandles) {
            setTimeout(() => {
                showCakeMessage();
            }, 500);

            setTimeout(() => {
                showWishMessage();
            }, 2000);

            setTimeout(() => {
                showNextSection('finalSection');
            }, 4000);
        }
    }
}

// Show birthday message on cake
function showCakeMessage() {
    const cakeHolder = document.getElementById('cake-holder');
    if (cakeHolder) {
        cakeHolder.classList.add('done');
    }

    // After showing cake message, automatically go to final section
    setTimeout(() => {
        showFinalSection();
    }, 3000);
}

// Show final section
function showFinalSection() {
    const currentSection = document.querySelector('.section:not(.hidden)');
    const finalSection = document.getElementById('finalSection');

    if (currentSection) {
        currentSection.classList.add('hidden');
    }

    if (finalSection) {
        finalSection.classList.remove('hidden');
    }
}

// Create beautiful blow effect for candles
function createBlowEffect(flame) {
    const rect = flame.getBoundingClientRect();

    // Create multiple particles for a more realistic effect
    for (let i = 0; i < 8; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'fixed';
        particle.style.left = (rect.left + rect.width / 2 - 5) + 'px';
        particle.style.top = (rect.top + rect.height / 2 - 5) + 'px';
        particle.style.width = '10px';
        particle.style.height = '10px';
        particle.style.background = `linear-gradient(45deg, #ffb6c1, #ffc0cb, #ffffff)`;
        particle.style.borderRadius = '50%';
        particle.style.pointerEvents = 'none';
        particle.style.zIndex = '1000';
        particle.style.boxShadow = '0 0 10px rgba(255, 182, 193, 0.8)';

        // Random direction for each particle
        const angle = (i * 45) + Math.random() * 30;
        const distance = 30 + Math.random() * 40;
        particle.style.setProperty('--angle', angle + 'deg');
        particle.style.setProperty('--distance', distance + 'px');
        particle.style.animation = 'blowParticle 1.2s ease-out forwards';

        document.body.appendChild(particle);

        setTimeout(() => {
            if (particle.parentNode) {
                document.body.removeChild(particle);
            }
        }, 1200);
    }

    // Create a beautiful smoke effect
    const smoke = document.createElement('div');
    smoke.innerHTML = '💨';
    smoke.style.position = 'fixed';
    smoke.style.left = (rect.left + rect.width / 2 - 10) + 'px';
    smoke.style.top = (rect.top - 10) + 'px';
    smoke.style.fontSize = '1.5rem';
    smoke.style.pointerEvents = 'none';
    smoke.style.zIndex = '1000';
    smoke.style.animation = 'smokeEffect 2s ease-out forwards';

    document.body.appendChild(smoke);

    setTimeout(() => {
        if (smoke.parentNode) {
            document.body.removeChild(smoke);
        }
    }, 2000);
}

// Add beautiful blow effect animations
const blowEffectStyle = document.createElement('style');
blowEffectStyle.textContent = `
    @keyframes blowParticle {
        0% {
            transform: translate(0, 0) scale(1);
            opacity: 1;
        }
        50% {
            transform: translate(
                calc(cos(var(--angle)) * var(--distance)),
                calc(sin(var(--angle)) * var(--distance))
            ) scale(1.2);
            opacity: 0.8;
        }
        100% {
            transform: translate(
                calc(cos(var(--angle)) * calc(var(--distance) * 2)),
                calc(sin(var(--angle)) * calc(var(--distance) * 2))
            ) scale(0);
            opacity: 0;
        }
    }

    @keyframes smokeEffect {
        0% {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
        100% {
            transform: translateY(-50px) scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(blowEffectStyle);

// Show next section
function showNextSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.remove('hidden');
        section.style.opacity = '0';
        section.style.transition = 'opacity 1s ease-in';
        
        // Smooth scroll to section
        section.scrollIntoView({ behavior: 'smooth' });
        
        setTimeout(() => {
            section.style.opacity = '1';
        }, 100);
    }
}

// Show wish message
function showWishMessage() {
    const wishMessage = document.getElementById('wishMessage');
    if (wishMessage) {
        wishMessage.classList.remove('hidden');
        wishMessage.style.animation = 'fadeInUp 1s ease-out';
    }
}



// Celebration function
function celebrate() {
    // Show full-screen birthday celebration
    showBirthdayCelebration();
}

// Create confetti effect
function createConfetti() {
    const confettiContainer = document.getElementById('confettiContainer');
    const colors = ['#ff6b9d', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
    
    for (let i = 0; i < 100; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.background = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.animationDelay = Math.random() * 3 + 's';
        confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
        
        confettiContainer.appendChild(confetti);
        
        // Remove confetti after animation
        setTimeout(() => {
            if (confetti.parentNode) {
                confetti.parentNode.removeChild(confetti);
            }
        }, 5000);
    }
}

// Play birthday music (using Web Audio API)
function playBirthdayMusic() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const notes = [
            { freq: 261.63, duration: 0.5 }, // C
            { freq: 261.63, duration: 0.5 }, // C
            { freq: 293.66, duration: 1 },   // D
            { freq: 261.63, duration: 1 },   // C
            { freq: 349.23, duration: 1 },   // F
            { freq: 329.63, duration: 2 }    // E
        ];
        
        let currentTime = audioContext.currentTime;
        
        notes.forEach(note => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(note.freq, currentTime);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.1);
            gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);
            
            oscillator.start(currentTime);
            oscillator.stop(currentTime + note.duration);
            
            currentTime += note.duration;
        });
    } catch (error) {
        console.log('Audio not supported');
    }
}

// Show final message
function showFinalMessage() {
    setTimeout(() => {
        alert('🎉 Happy Birthday, Vaishnavi! Thank you for being the most wonderful person in my life! 💕');
    }, 1000);
}

// Add floating hearts periodically
setInterval(() => {
    if (Math.random() < 0.3) {
        createFloatingHeart();
    }
}, 2000);

function createFloatingHeart() {
    const heart = document.createElement('div');
    heart.innerHTML = '💖';
    heart.style.position = 'fixed';
    heart.style.left = Math.random() * window.innerWidth + 'px';
    heart.style.top = window.innerHeight + 'px';
    heart.style.fontSize = '2rem';
    heart.style.pointerEvents = 'none';
    heart.style.zIndex = '1';
    heart.style.animation = 'floatUp 4s ease-out forwards';
    
    document.body.appendChild(heart);
    
    setTimeout(() => {
        if (heart.parentNode) {
            heart.parentNode.removeChild(heart);
        }
    }, 4000);
}

// Add float up animation
const floatUpStyle = document.createElement('style');
floatUpStyle.textContent = `
    @keyframes floatUp {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(floatUpStyle);

// Add smooth scrolling behavior
document.documentElement.style.scrollBehavior = 'smooth';

// Add touch support for mobile devices
document.addEventListener('touchstart', function() {}, { passive: true });

// Initialize flowers animation
document.addEventListener('DOMContentLoaded', function() {
    // Start flowers animations immediately
    setTimeout(() => {
        document.body.classList.remove('not-loaded');
    }, 1000);
});

// Full-Screen Birthday Celebration Functions
function showBirthdayCelebration() {
    const overlay = document.getElementById('birthdayCelebrationOverlay');
    overlay.classList.add('active');

    // Initialize and start advanced fireworks
    setTimeout(() => {
        initializeFireworks();
    }, 500);

    // Add entrance animation to neon text
    setTimeout(() => {
        const neonText = overlay.querySelector('.neon-text');
        const neonName = overlay.querySelector('.neon-name');
        neonText.style.animation = 'fadeInUp 1s ease-out';
        neonName.style.animation = 'fadeInUp 1s ease-out 0.5s both';
    }, 1000);

    // Play birthday music
    playBirthdayMusic();
}

function closeCelebration() {
    const overlay = document.getElementById('birthdayCelebrationOverlay');
    overlay.classList.remove('active');

    // Stop fireworks animation
    if (window.birthdayFireworks) {
        window.birthdayFireworks.stop();
    }
}

// Advanced Fireworks Animation (Based on CodePen)
const PI2 = Math.PI * 2;
const random = (min, max) => Math.random() * (max - min + 1) + min | 0;
const timestamp = _ => new Date().getTime();

class BirthdayFireworks {
    constructor() {
        this.canvas = document.getElementById('fireworksCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.resize();

        this.fireworks = [];
        this.counter = 0;
        this.isRunning = false;
        this.animationId = null;
        this.then = timestamp();
    }

    resize() {
        this.width = this.canvas.width = window.innerWidth;
        let center = this.width / 2 | 0;
        this.spawnA = center - center / 4 | 0;
        this.spawnB = center + center / 4 | 0;

        this.height = this.canvas.height = window.innerHeight;
        this.spawnC = this.height * .1;
        this.spawnD = this.height * .5;
    }

    onClick(evt) {
        let x = evt.clientX || evt.touches && evt.touches[0].pageX;
        let y = evt.clientY || evt.touches && evt.touches[0].pageY;

        let count = random(3, 5);
        for(let i = 0; i < count; i++) {
            this.fireworks.push(new Firework(
                random(this.spawnA, this.spawnB),
                this.height,
                x,
                y,
                random(300, 360), // Light pink hue range
                random(30, 110)
            ));
        }
        this.counter = -1;
    }

    update(delta) {
        // Clear canvas with slight fade effect to create trails, but keep stars visible
        this.ctx.globalCompositeOperation = 'destination-out';
        this.ctx.fillStyle = `rgba(0,0,0,${0.1})`;
        this.ctx.fillRect(0, 0, this.width, this.height);

        this.ctx.globalCompositeOperation = 'lighter';
        for (let firework of this.fireworks) {
            firework.update(delta, this.ctx);
        }

        // Create new fireworks automatically
        this.counter += delta * 2;
        if (this.counter >= 1) {
            this.fireworks.push(new Firework(
                random(this.spawnA, this.spawnB),
                this.height,
                random(0, this.width),
                random(this.spawnC, this.spawnD),
                random(300, 360), // Light pink hue range
                random(30, 110)
            ));
            this.counter = 0;
        }

        // Clean up dead fireworks
        if (this.fireworks.length > 1000) {
            this.fireworks = this.fireworks.filter(firework => !firework.dead);
        }
    }

    start() {
        this.isRunning = true;
        this.loop();

        // Add click listener for interactive fireworks
        this.canvas.addEventListener('click', (evt) => this.onClick(evt));
        this.canvas.addEventListener('touchstart', (evt) => this.onClick(evt));
    }

    stop() {
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        this.canvas.removeEventListener('click', this.onClick);
        this.canvas.removeEventListener('touchstart', this.onClick);
    }

    loop() {
        if (!this.isRunning) return;

        this.animationId = requestAnimationFrame(() => this.loop());

        let now = timestamp();
        let delta = now - this.then;
        this.then = now;
        this.update(delta / 1000);
    }
}

class Firework {
    constructor(x, y, targetX, targetY, shade, offsprings) {
        this.dead = false;
        this.offsprings = offsprings;

        this.x = x;
        this.y = y;
        this.targetX = targetX;
        this.targetY = targetY;

        this.shade = shade;
        this.history = [];
    }

    update(delta, ctx) {
        if (this.dead) return;

        let xDiff = this.targetX - this.x;
        let yDiff = this.targetY - this.y;

        if (Math.abs(xDiff) > 3 || Math.abs(yDiff) > 3) {
            this.x += xDiff * 2 * delta;
            this.y += yDiff * 2 * delta;

            this.history.push({
                x: this.x,
                y: this.y
            });

            if (this.history.length > 20) this.history.shift();
        } else {
            if (this.offsprings && !this.madeChilds) {
                let babies = this.offsprings / 2;
                for (let i = 0; i < babies; i++) {
                    let targetX = this.x + this.offsprings * Math.cos(PI2 * i / babies) | 0;
                    let targetY = this.y + this.offsprings * Math.sin(PI2 * i / babies) | 0;

                    window.birthdayFireworks.fireworks.push(new Firework(
                        this.x, this.y, targetX, targetY, this.shade, 0
                    ));
                }
            }
            this.madeChilds = true;
            this.history.shift();
        }

        if (this.history.length === 0) {
            this.dead = true;
        } else if (this.offsprings) {
            for (let i = 0; this.history.length > i; i++) {
                let point = this.history[i];
                ctx.beginPath();
                ctx.fillStyle = 'hsl(' + this.shade + ',100%,' + i + '%)';
                ctx.arc(point.x, point.y, 1, 0, PI2, false);
                ctx.fill();
            }
        } else {
            ctx.beginPath();
            ctx.fillStyle = 'hsl(' + this.shade + ',100%,50%)';
            ctx.arc(this.x, this.y, 1, 0, PI2, false);
            ctx.fill();
        }
    }
}

function initializeFireworks() {
    window.birthdayFireworks = new BirthdayFireworks();
    window.birthdayFireworks.start();

    // Auto-stop after 60 seconds
    setTimeout(() => {
        if (window.birthdayFireworks) {
            window.birthdayFireworks.stop();
        }
    }, 60000);
}
